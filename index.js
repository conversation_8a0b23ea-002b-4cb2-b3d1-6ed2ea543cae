const express = require('express');
const app= express();
const cookiePareser= require('cookie-parser');
const userModel= require("./models/users");
app.set("view engine", "ejs");
app.use(express.json())
app.use(express.urlencoded({extended:true}))
app.use(cookiePareser())




app.get("/",(req,res)=>
{
    res.render("index")
})
        

app.listen(2000,()=>
{
    console.log("server is running on port 2000");
})