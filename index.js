const express = require('express');
const app= express();
const cookiePareser= require('cookie-parser');
const userModel= require("./models/users");
app.set("view engine", "ejs");
app.use(express.json())
app.use(express.urlencoded({extended:true}))
app.use(cookiePareser())
const jwt= require('jsonwebtoken');
const bcrypt=require('bcrypt');




app.get("/",(req,res)=>
{
    res.render("signup")
})

app.get("/signin",(req,res)=>
{
    res.render("signin")
})

app.post("/register",async (req, res)=>
{
    const {name, email, password, age, username}=req.body;
    user=userModel.findOne({email});

    bcrypt.genSalt(10, function(err, salt) {
    bcrypt.hash(password, salt, function(err, hash) {
        userModel.create(
            {
                username,
                email,
                name,
                age,
                password:hash
            }
        )

        let token=jwt.sign({email,userid:user._id},"key");
        res.cookie("token",token);
        res.send();
    });
});

app.post("/login",(req, res)=>
{
    const {email, password}= req.body;

    user=userModel.findOne({email});

    if(!user) return res.send("user not found");
    
    bcrypt.genSalt(10, function(err, salt) {
    bcrypt.hash(password, salt, function(err, hash) {
        bcrypt.compare(password, hash, function(err, result) {
            if(result)
            {
                return res.redirect(home);
            }

        return res.send("invalid password");
});
    });
});
})

})
        

app.listen(2000,()=>
{
    console.log("server is running on port 2000");
})