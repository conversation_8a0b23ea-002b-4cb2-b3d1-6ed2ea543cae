const mongoose= require('mongoose');


mongoose.connect("mongodb://127.0.0.1:27017/practicepro");

const postSchema= mongoose.schema(
    {
        user:{
            type:mongoose.Schema.Types.ObjectId,
            ref:"user"
        },
        date:{
            type:Date,
            default:Date.now    
        },

        content:String,

        likes:[
            {   type:mongoose.schema.Types.ObjectId,
                ref:'user'
            }
        ]
    }
);


module.exports=mongoose.model('post',postSchema);